import request from 'supertest';
import {
  app,
  setupTestFolders,
  cleanupDatabase,
  setupTestData,
  cleanupFiles,
} from '../../../core/utils/test/integration-setup.js';
import { createTestUser, getAuthToken } from '../../../core/utils/test/auth.js';
import { prisma } from '../../../core/utils/prisma.js';
import bcrypt from 'bcryptjs';

/**
 * اختبارات تكامل وحدة المصادقة
 */
describe('Authentication Integration Tests', () => {
  let userId: string;
  let refreshToken: string;
  let testUser: any;
  let testUsername: string;
  let testPassword: string;
  let authToken: string;

  // قبل جميع الاختبارات
  beforeAll(async () => {
    await setupTestFolders();
    await cleanupDatabase();

    // إنشاء بيانات الاختبار
    const { user, authToken: token } = await setupTestData();
    testUser = user;
    userId = user.id;
    testUsername = user.username;
    testPassword = 'Test@123'; // كلمة المرور الافتراضية
    authToken = token;

    console.log('✅ تم إعداد مستخدم الاختبار للمصادقة:', {
      id: userId,
      username: testUsername,
      hasPassword: !!testUser.password,
      isActive: testUser.isActive
    });
  });

  // قبل كل اختبار
  beforeEach(async () => {
    // التأكد من أن المستخدم موجود ونشط
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      // إعادة إنشاء المستخدم إذا لم يكن موجوداً
      const { user, authToken: token } = await setupTestData();
      testUser = user;
      userId = user.id;
      testUsername = user.username;
      authToken = token;
      console.log('🔄 تم إعادة إنشاء مستخدم الاختبار:', {
        id: userId,
        username: testUsername
      });
    } else if (!existingUser.isActive) {
      // تفعيل المستخدم إذا كان غير نشط
      await prisma.user.update({
        where: { id: userId },
        data: { isActive: true }
      });
      console.log('🔄 تم تفعيل مستخدم الاختبار');
    } else {
      // التأكد من أن كلمة المرور صحيحة
      const isPasswordValid = await bcrypt.compare(testPassword, existingUser.password || '');
      if (!isPasswordValid) {
        // إعادة تعيين كلمة المرور إذا لم تكن صحيحة
        const hashedPassword = await bcrypt.hash(testPassword, 12);
        await prisma.user.update({
          where: { id: userId },
          data: { password: hashedPassword }
        });
        console.log('🔄 تم إعادة تعيين كلمة المرور للمستخدم');
      }
    }
  });

  /**
   * اختبار تسجيل الدخول
   */
  describe('POST /api/auth/login', () => {
    it('يجب أن يسجل الدخول بنجاح مع بيانات صحيحة', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: testUsername,
          password: testPassword,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
      expect(response.body.data.user).toBeDefined();
      expect(response.body.data.user.username).toBe(testUsername);

      // حفظ رمز التحديث للاختبارات اللاحقة
      refreshToken = response.body.data.refreshToken;
    });

    it('يجب أن يرفض تسجيل الدخول مع كلمة مرور خاطئة', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: testUsername,
          password: 'wrong_password',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });

    it('يجب أن يرفض تسجيل الدخول مع اسم مستخدم غير موجود', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'non_existent_user',
          password: testPassword,
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  /**
   * اختبار تحديث الرمز المميز
   */
  describe('POST /api/auth/refresh-token', () => {
    it('يجب أن يحدث الرمز المميز بنجاح', async () => {
      // تسجيل الدخول أولاً للحصول على refresh token
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: testUsername,
          password: testPassword,
        })
        .expect(200);

      const currentRefreshToken = loginResponse.body.data.refreshToken;

      const response = await request(app)
        .post('/api/auth/refresh-token')
        .send({
          refreshToken: currentRefreshToken,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
    });

    it('يجب أن يرفض تحديث الرمز المميز مع رمز تحديث غير صالح', async () => {
      const response = await request(app)
        .post('/api/auth/refresh-token')
        .send({
          refreshToken: 'invalid_refresh_token',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  /**
   * اختبار تسجيل مستخدم جديد
   */
  describe('POST /api/auth/register', () => {
    it('يجب أن يسجل مستخدم جديد بنجاح', async () => {
      // تسجيل الدخول أولاً للحصول على رمز مميز للمسؤول
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: testUsername,
          password: testPassword,
        })
        .expect(200);

      const adminToken = loginResponse.body.data.token;

      // تسجيل مستخدم جديد
      const response = await request(app)
        .post('/api/auth/register')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'new_test_user',
          password: 'NewTest@123',
          name: 'مستخدم اختبار جديد',
          email: '<EMAIL>',
          role: 'USER',
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.username).toBe('new_test_user');
      expect(response.body.data.name).toBe('مستخدم اختبار جديد');
      expect(response.body.data.email).toBe('<EMAIL>');
      expect(response.body.data.role).toBe('USER');

      // التحقق من إنشاء المستخدم في قاعدة البيانات
      const createdUser = await prisma.user.findUnique({
        where: { username: 'new_test_user' },
      });
      expect(createdUser).not.toBeNull();
      expect(createdUser?.username).toBe('new_test_user');
      expect(createdUser?.name).toBe('مستخدم اختبار جديد');
      expect(createdUser?.email).toBe('<EMAIL>');
      expect(createdUser?.role).toBe('USER');

      // التحقق من تشفير كلمة المرور
      const isPasswordValid = await bcrypt.compare('NewTest@123', createdUser?.password || '');
      expect(isPasswordValid).toBe(true);

      // حذف المستخدم المنشأ للاختبار
      await prisma.user.delete({
        where: { username: 'new_test_user' },
      });
    });

    it('يجب أن يرفض تسجيل مستخدم موجود بالفعل', async () => {
      // تسجيل الدخول أولاً للحصول على رمز مميز للمسؤول
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: testUsername,
          password: testPassword,
        })
        .expect(200);

      const adminToken = loginResponse.body.data.token;

      // محاولة تسجيل مستخدم موجود بالفعل
      const response = await request(app)
        .post('/api/auth/register')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: testUsername, // اسم مستخدم موجود بالفعل
          password: testPassword,
          name: 'مستخدم اختبار',
          email: '<EMAIL>',
          role: 'USER',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  /**
   * اختبار تغيير كلمة المرور
   */
  describe('POST /api/auth/change-password', () => {
    it('يجب أن يغير كلمة المرور بنجاح', async () => {
      // تسجيل الدخول أولاً للحصول على رمز مميز
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: testUsername,
          password: testPassword,
        })
        .expect(200);

      const token = loginResponse.body.data.token;

      // تغيير كلمة المرور
      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send({
          currentPassword: testPassword,
          newPassword: 'NewTest@123',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();

      // التحقق من تغيير كلمة المرور
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });
      const isNewPasswordValid = await bcrypt.compare('NewTest@123', user?.password || '');
      expect(isNewPasswordValid).toBe(true);

      // إعادة كلمة المرور إلى القيمة الأصلية
      const hashedPassword = await bcrypt.hash(testPassword, 12); // استخدام نفس BCRYPT_ROUNDS
      await prisma.user.update({
        where: { id: userId },
        data: { password: hashedPassword },
      });
    });

    it('يجب أن يرفض تغيير كلمة المرور مع كلمة مرور حالية خاطئة', async () => {
      // تسجيل الدخول أولاً للحصول على رمز مميز
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: testUsername,
          password: testPassword,
        })
        .expect(200);

      const token = loginResponse.body.data.token;

      // محاولة تغيير كلمة المرور مع كلمة مرور حالية خاطئة
      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send({
          currentPassword: 'wrong_password',
          newPassword: 'NewTest@123',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  // تنظيف البيانات بعد انتهاء جميع الاختبارات
  afterAll(async () => {
    try {
      await cleanupDatabase();
      console.log('تم تنظيف بيانات اختبار المصادقة');
    } catch (error) {
      console.error('خطأ في تنظيف بيانات اختبار المصادقة:', error);
    }
  });
});
